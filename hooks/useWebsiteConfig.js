import { useState, useEffect, useContext, createContext } from 'react';
import { useMediaQuery } from 'react-responsive';
import configManager from '../utils/config-manager.js';

/**
 * React hooks and context for managing website configuration
 */

// Create configuration context
const ConfigContext = createContext(null);

/**
 * Configuration Provider Component
 * Wraps the app and provides configuration to all child components
 */
export const ConfigProvider = ({ children, configSource = '/config/website-config.json' }) => {
  const [config, setConfig] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const loadConfiguration = async () => {
      try {
        setLoading(true);
        const loadedConfig = await configManager.loadConfig(configSource);
        setConfig(loadedConfig);
        setError(null);
      } catch (err) {
        setError(err.message);
        console.error('Failed to load configuration:', err);
      } finally {
        setLoading(false);
      }
    };

    loadConfiguration();
  }, [configSource]);

  const value = {
    config,
    loading,
    error,
    updateSection: (sectionName, newData) => {
      const success = configManager.updateSection(sectionName, newData);
      if (success) {
        setConfig(configManager.getConfig());
      }
      return success;
    },
    reloadConfig: async () => {
      try {
        setLoading(true);
        const reloadedConfig = await configManager.loadConfig(configSource);
        setConfig(reloadedConfig);
        setError(null);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    }
  };

  return (
    <ConfigContext.Provider value={value}>
      {children}
    </ConfigContext.Provider>
  );
};

/**
 * Hook to access the website configuration
 * @returns {object} Configuration context
 */
export const useWebsiteConfig = () => {
  const context = useContext(ConfigContext);
  if (!context) {
    throw new Error('useWebsiteConfig must be used within a ConfigProvider');
  }
  return context;
};

/**
 * Hook to get a specific section of the configuration
 * @param {string} sectionName - Name of the section
 * @returns {object} Section configuration and utilities
 */
export const useConfigSection = (sectionName) => {
  const { config, updateSection } = useWebsiteConfig();
  
  const section = config?.sections?.[sectionName] || null;
  
  return {
    data: section,
    update: (newData) => updateSection(sectionName, newData),
    isLoaded: !!section
  };
};

/**
 * Hook to get branding configuration
 * @returns {object} Branding configuration
 */
export const useBranding = () => {
  const { config } = useWebsiteConfig();
  return config?.branding || null;
};

/**
 * Hook to get animation configuration
 * @returns {object} Animation configuration
 */
export const useAnimationConfig = () => {
  const { config } = useWebsiteConfig();
  return config?.animations || null;
};

/**
 * Hook to get layout configuration
 * @returns {object} Layout configuration
 */
export const useLayoutConfig = () => {
  const { config } = useWebsiteConfig();
  return config?.layout || null;
};

/**
 * Hook to handle responsive values based on current breakpoint
 * @param {string|object} value - Responsive value
 * @returns {string} Resolved value for current breakpoint
 */
export const useResponsiveValue = (value) => {
  const { config } = useWebsiteConfig();
  
  const isMobile = useMediaQuery({ query: '(max-width: 768px)' });
  const isTablet = useMediaQuery({ query: '(max-width: 1024px)' });
  const isDesktop = useMediaQuery({ query: '(min-width: 1025px)' });
  const isXL = useMediaQuery({ query: '(min-width: 1280px)' });
  const is2XL = useMediaQuery({ query: '(min-width: 1536px)' });
  
  const getCurrentBreakpoint = () => {
    if (is2XL) return '2xl';
    if (isXL) return 'xl';
    if (isDesktop) return 'desktop';
    if (isTablet) return 'tablet';
    if (isMobile) return 'mobile';
    return 'desktop';
  };
  
  const breakpoint = getCurrentBreakpoint();
  
  return configManager.getResponsiveValue(value, breakpoint);
};

/**
 * Hook to get CSS custom properties from configuration
 * @returns {object} CSS custom properties object
 */
export const useCSSCustomProperties = () => {
  const { config } = useWebsiteConfig();
  
  if (!config) return {};
  
  const { colors, fonts } = config.branding;
  const customProperties = {};
  
  // Add color variables
  Object.entries(colors).forEach(([key, value]) => {
    customProperties[`--color-${key}`] = value;
  });
  
  // Add font variables
  Object.entries(fonts).forEach(([key, value]) => {
    customProperties[`--font-${key}`] = value;
  });
  
  return customProperties;
};

/**
 * Hook to get GSAP animation settings for a specific animation type
 * @param {string} animationType - Type of animation ('textReveal', 'scroll', etc.)
 * @returns {object} Animation settings
 */
export const useGSAPSettings = (animationType = 'global') => {
  const animationConfig = useAnimationConfig();
  
  if (!animationConfig) return {};
  
  if (animationType === 'global') {
    return animationConfig.globalSettings || {};
  }
  
  return animationConfig[animationType] || {};
};

/**
 * Hook to get asset paths and validate them
 * @returns {object} Asset information and validation utilities
 */
export const useAssets = () => {
  const { config } = useWebsiteConfig();
  const [assetValidation, setAssetValidation] = useState(null);
  const [validating, setValidating] = useState(false);
  
  const validateAssets = async () => {
    if (!config) return;
    
    setValidating(true);
    try {
      const validation = await configManager.validateAssets();
      setAssetValidation(validation);
    } catch (error) {
      console.error('Asset validation failed:', error);
    } finally {
      setValidating(false);
    }
  };
  
  useEffect(() => {
    if (config) {
      validateAssets();
    }
  }, [config]);
  
  return {
    assetPaths: config ? configManager.getAssetPaths() : [],
    validation: assetValidation,
    isValidating: validating,
    revalidate: validateAssets
  };
};

/**
 * Hook to get theme colors with CSS variable fallbacks
 * @returns {object} Theme colors
 */
export const useThemeColors = () => {
  const branding = useBranding();
  
  if (!branding) return {};
  
  const colors = {};
  Object.entries(branding.colors).forEach(([key, value]) => {
    colors[key] = value;
    colors[`${key}Var`] = `var(--color-${key}, ${value})`;
  });
  
  return colors;
};

/**
 * Hook to get typography settings
 * @returns {object} Typography configuration
 */
export const useTypography = () => {
  const branding = useBranding();
  
  if (!branding) return {};
  
  return {
    fonts: branding.fonts,
    primary: branding.fonts?.primary || 'Antonio, sans-serif',
    secondary: branding.fonts?.secondary || 'ProximaNova, sans-serif',
    primaryVar: `var(--font-primary, ${branding.fonts?.primary || 'Antonio, sans-serif'})`,
    secondaryVar: `var(--font-secondary, ${branding.fonts?.secondary || 'ProximaNova, sans-serif'})`
  };
};

// Example usage in components:
/*
import { useConfigSection, useResponsiveValue, useThemeColors } from './hooks/useWebsiteConfig';

const HeroSection = () => {
  const { data: heroConfig, update } = useConfigSection('hero');
  const colors = useThemeColors();
  const buttonPadding = useResponsiveValue(heroConfig?.button?.padding);
  
  if (!heroConfig) return <div>Loading...</div>;
  
  return (
    <section style={{ backgroundColor: colors.background }}>
      <h1>{heroConfig.title}</h1>
      <p>{heroConfig.description}</p>
      <button 
        style={{ 
          padding: buttonPadding,
          backgroundColor: heroConfig.button.backgroundColor 
        }}
      >
        {heroConfig.button.text}
      </button>
    </section>
  );
};
*/
