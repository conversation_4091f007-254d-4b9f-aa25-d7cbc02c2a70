# GSAP Awwwards Website - Schema Implementation Plan

## Overview

This document outlines the comprehensive implementation plan for integrating Zod schemas into the GSAP Awwwards Website, enabling programmatic content management and dynamic updates.

## 1. Integration Strategy

### Phase 1: Foundation Setup (Week 1)

#### Install Dependencies
```bash
npm install zod
```

#### Project Structure
```
src/
├── schemas/
│   ├── website-config.js      # Main Zod schemas
│   └── validation.js          # Additional validation utilities
├── config/
│   ├── website-config.json    # Main configuration file
│   └── environments/          # Environment-specific configs
├── utils/
│   ├── config-manager.js      # Configuration management utilities
│   └── api-client.js          # API client for config updates
├── hooks/
│   ├── useWebsiteConfig.js    # React hooks for config
│   └── useGSAPConfig.js       # GSAP-specific hooks
├── components/
│   └── ConfigProvider.jsx     # Configuration context provider
└── api/
    ├── config/
    │   ├── GET.js             # Get configuration
    │   ├── PUT.js             # Update configuration
    │   └── validate.js        # Validation endpoint
    └── assets/
        └── validate.js        # Asset validation endpoint
```

### Phase 2: Core Implementation (Week 2-3)

#### 1. Configuration Provider Setup

Create a root-level configuration provider:

```jsx
// src/main.jsx
import { ConfigProvider } from './hooks/useWebsiteConfig';

ReactDOM.createRoot(document.getElementById('root')).render(
  <ConfigProvider configSource="/config/website-config.json">
    <App />
  </ConfigProvider>
);
```

#### 2. Component Migration

Migrate existing components to use configuration:

```jsx
// Before (hardcoded)
const HeroSection = () => {
  return (
    <section className="bg-main-bg">
      <h1 className="hero-title">Freaking Delicious</h1>
      {/* ... */}
    </section>
  );
};

// After (configuration-driven)
const HeroSection = () => {
  const { data: heroConfig } = useConfigSection('hero');
  const colors = useThemeColors();
  
  if (!heroConfig) return <div>Loading...</div>;
  
  return (
    <section style={{ backgroundColor: colors.background }}>
      <h1 className="hero-title">{heroConfig.title}</h1>
      {/* ... */}
    </section>
  );
};
```

### Phase 3: API Development (Week 3-4)

#### Configuration Management API

```javascript
// api/config/GET.js
export async function GET(request) {
  try {
    const config = await configManager.getConfig();
    return Response.json(config);
  } catch (error) {
    return Response.json({ error: error.message }, { status: 500 });
  }
}

// api/config/PUT.js
export async function PUT(request) {
  try {
    const newConfig = await request.json();
    const validatedConfig = WebsiteConfigSchema.parse(newConfig);
    
    // Save to file or database
    await saveConfig(validatedConfig);
    
    return Response.json({ success: true, config: validatedConfig });
  } catch (error) {
    return Response.json({ error: error.message }, { status: 400 });
  }
}
```

## 2. API Structure

### RESTful Configuration API

#### Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/api/config` | Get full configuration |
| `PUT` | `/api/config` | Update full configuration |
| `GET` | `/api/config/sections/{section}` | Get specific section |
| `PUT` | `/api/config/sections/{section}` | Update specific section |
| `POST` | `/api/config/validate` | Validate configuration |
| `GET` | `/api/assets/validate` | Validate all assets |
| `GET` | `/api/config/schema` | Get Zod schema definitions |

#### Request/Response Examples

**Get Hero Section:**
```http
GET /api/config/sections/hero
```

```json
{
  "data": {
    "title": "Freaking Delicious",
    "subtitle": "Protein + Caffine",
    "description": "Live life to the fullest...",
    "button": { /* ... */ }
  }
}
```

**Update Flavor Section:**
```http
PUT /api/config/sections/flavors
Content-Type: application/json

{
  "title": {
    "firstLine": "We have 7",
    "highlightText": "amazing",
    "lastLine": "delicious flavors"
  },
  "flavors": [/* ... */]
}
```

### GraphQL Alternative (Optional)

```graphql
type Query {
  config: WebsiteConfig
  section(name: String!): SectionConfig
  validateAssets: AssetValidation
}

type Mutation {
  updateConfig(input: WebsiteConfigInput!): WebsiteConfig
  updateSection(name: String!, input: SectionConfigInput!): SectionConfig
}
```

## 3. Validation Implementation

### Client-Side Validation

```javascript
// utils/validation.js
import { WebsiteConfigSchema } from '../schemas/website-config.js';

export const validateConfig = (config) => {
  try {
    return {
      isValid: true,
      data: WebsiteConfigSchema.parse(config),
      errors: []
    };
  } catch (error) {
    return {
      isValid: false,
      data: null,
      errors: error.errors
    };
  }
};

export const validateSection = (sectionName, data) => {
  const schemas = {
    hero: HeroSectionSchema,
    flavors: FlavorSectionSchema,
    // ... other schemas
  };
  
  const schema = schemas[sectionName];
  if (!schema) {
    return { isValid: false, errors: ['Unknown section'] };
  }
  
  try {
    return {
      isValid: true,
      data: schema.parse(data),
      errors: []
    };
  } catch (error) {
    return {
      isValid: false,
      data: null,
      errors: error.errors
    };
  }
};
```

### Server-Side Validation

```javascript
// api/middleware/validation.js
export const validateConfigMiddleware = (schema) => {
  return async (request, response, next) => {
    try {
      const body = await request.json();
      const validatedData = schema.parse(body);
      request.validatedData = validatedData;
      next();
    } catch (error) {
      return Response.json({
        error: 'Validation failed',
        details: error.errors
      }, { status: 400 });
    }
  };
};
```

## 4. Migration Strategy

### Step 1: Audit Current Implementation

1. **Identify Hardcoded Values:**
   - Text content in components
   - Color values in CSS/Tailwind classes
   - Asset paths
   - Animation parameters

2. **Map to Schema Structure:**
   - Create mapping document
   - Identify missing schema fields
   - Plan data transformation

### Step 2: Gradual Migration

#### Week 1: Core Data Migration
```javascript
// Extract current constants to configuration
const currentConstants = {
  flavorlists: [/* existing data */],
  nutrientLists: [/* existing data */],
  cards: [/* existing data */]
};

// Transform to new schema format
const migratedConfig = {
  sections: {
    flavors: {
      flavors: currentConstants.flavorlists.map(flavor => ({
        name: flavor.name,
        color: flavor.color,
        rotation: flavor.rotation,
        backgroundImage: `/images/${flavor.color}-bg.svg`,
        drinkImage: `/images/${flavor.color}-drink.webp`,
        elementsImage: `/images/${flavor.color}-elements.webp`
      }))
    },
    nutrition: {
      nutrients: currentConstants.nutrientLists
    },
    testimonials: {
      cards: currentConstants.cards
    }
  }
};
```

#### Week 2: Component Migration
```javascript
// Migration utility
const migrateComponent = (ComponentName, configSection) => {
  const OriginalComponent = ComponentName;
  
  return (props) => {
    const { data: config } = useConfigSection(configSection);
    
    if (!config) {
      // Fallback to original hardcoded version during migration
      return <OriginalComponent {...props} />;
    }
    
    // Use configuration-driven version
    return <ConfigurableComponent config={config} {...props} />;
  };
};
```

### Step 3: Testing Strategy

#### Unit Tests
```javascript
// tests/schemas/website-config.test.js
import { WebsiteConfigSchema } from '../src/schemas/website-config.js';

describe('Website Configuration Schema', () => {
  test('validates complete configuration', () => {
    const validConfig = {/* valid config */};
    expect(() => WebsiteConfigSchema.parse(validConfig)).not.toThrow();
  });
  
  test('rejects invalid configuration', () => {
    const invalidConfig = {/* invalid config */};
    expect(() => WebsiteConfigSchema.parse(invalidConfig)).toThrow();
  });
});
```

#### Integration Tests
```javascript
// tests/api/config.test.js
describe('Configuration API', () => {
  test('GET /api/config returns valid configuration', async () => {
    const response = await fetch('/api/config');
    const config = await response.json();
    
    expect(() => WebsiteConfigSchema.parse(config)).not.toThrow();
  });
});
```

## 5. Content Management Interface

### Admin Dashboard (Optional)

Create a simple admin interface for non-technical users:

```jsx
// components/admin/ConfigEditor.jsx
const ConfigEditor = () => {
  const { config, updateSection } = useWebsiteConfig();
  const [editingSection, setEditingSection] = useState(null);
  
  return (
    <div className="admin-dashboard">
      <h1>Website Configuration</h1>
      
      {Object.entries(config.sections).map(([sectionName, sectionData]) => (
        <div key={sectionName} className="section-editor">
          <h2>{sectionName}</h2>
          <button onClick={() => setEditingSection(sectionName)}>
            Edit
          </button>
          
          {editingSection === sectionName && (
            <SectionEditor
              sectionName={sectionName}
              data={sectionData}
              onSave={(newData) => {
                updateSection(sectionName, newData);
                setEditingSection(null);
              }}
              onCancel={() => setEditingSection(null)}
            />
          )}
        </div>
      ))}
    </div>
  );
};
```

## 6. Performance Considerations

### Optimization Strategies

1. **Lazy Loading:** Load configuration sections on demand
2. **Caching:** Implement configuration caching with invalidation
3. **Bundling:** Separate configuration from main bundle
4. **Validation:** Client-side validation for immediate feedback

### Implementation

```javascript
// utils/config-cache.js
class ConfigCache {
  constructor(ttl = 300000) { // 5 minutes
    this.cache = new Map();
    this.ttl = ttl;
  }
  
  get(key) {
    const item = this.cache.get(key);
    if (!item) return null;
    
    if (Date.now() > item.expiry) {
      this.cache.delete(key);
      return null;
    }
    
    return item.data;
  }
  
  set(key, data) {
    this.cache.set(key, {
      data,
      expiry: Date.now() + this.ttl
    });
  }
}
```

## 7. Deployment Strategy

### Environment Configuration

```javascript
// config/environments/development.json
{
  "api": {
    "baseUrl": "http://localhost:3000",
    "timeout": 5000
  },
  "features": {
    "adminInterface": true,
    "configValidation": true
  }
}

// config/environments/production.json
{
  "api": {
    "baseUrl": "https://api.spylt.com",
    "timeout": 10000
  },
  "features": {
    "adminInterface": false,
    "configValidation": false
  }
}
```

### Build Process

```javascript
// vite.config.js
export default defineConfig({
  plugins: [
    react(),
    tailwindcss(),
    // Custom plugin to validate configuration at build time
    {
      name: 'validate-config',
      buildStart() {
        const config = JSON.parse(fs.readFileSync('./config/website-config.json'));
        try {
          WebsiteConfigSchema.parse(config);
          console.log('✅ Configuration validation passed');
        } catch (error) {
          console.error('❌ Configuration validation failed:', error);
          process.exit(1);
        }
      }
    }
  ]
});
```

## 8. Monitoring and Analytics

### Configuration Change Tracking

```javascript
// utils/analytics.js
export const trackConfigChange = (section, changes) => {
  // Send to analytics service
  analytics.track('config_updated', {
    section,
    changes,
    timestamp: new Date().toISOString(),
    user: getCurrentUser()
  });
};
```

## 9. Quick Start Guide

### Immediate Implementation Steps

1. **Install Zod:**
   ```bash
   npm install zod
   ```

2. **Copy Schema Files:**
   - Copy `schemas/website-config.js` to your project
   - Copy `utils/config-manager.js` for configuration management
   - Copy `hooks/useWebsiteConfig.js` for React integration

3. **Update Your App:**
   ```jsx
   // src/main.jsx
   import { ConfigProvider } from './hooks/useWebsiteConfig';

   ReactDOM.createRoot(document.getElementById('root')).render(
     <ConfigProvider>
       <App />
     </ConfigProvider>
   );
   ```

4. **Migrate One Component:**
   ```jsx
   // Example: Update HeroSection to use configuration
   const HeroSection = () => {
     const { data: heroConfig } = useConfigSection('hero');

     return (
       <section>
         <h1>{heroConfig?.title || 'Freaking Delicious'}</h1>
         {/* Gradual migration with fallbacks */}
       </section>
     );
   };
   ```

### Benefits Achieved

✅ **Type Safety:** Zod schemas ensure data integrity
✅ **Programmatic Control:** Modify content via API calls
✅ **Validation:** Automatic validation of all changes
✅ **Responsive Design:** Built-in responsive value handling
✅ **Asset Management:** Centralized asset path management
✅ **Animation Control:** Configurable GSAP animations
✅ **Theme Management:** Dynamic color and typography
✅ **Developer Experience:** IntelliSense and error catching

This implementation plan provides a comprehensive roadmap for transforming the GSAP Awwwards Website into a fully configurable, schema-driven system that enables easy content management and dynamic updates while maintaining type safety and validation.
