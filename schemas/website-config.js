import { z } from 'zod';

// ============================================================================
// CORE SCHEMAS
// ============================================================================

// Color Schema - Supports hex, rgb, hsl, and CSS custom properties
const ColorSchema = z.string().regex(/^(#[0-9A-Fa-f]{6}|#[0-9A-Fa-f]{3}|rgb\(.*\)|hsl\(.*\)|var\(--.*\)|[a-zA-Z]+)$/);

// Asset Path Schema - Validates asset paths
const AssetPathSchema = z.string().regex(/^\/(images|videos|fonts)\/.*\.(png|jpg|jpeg|webp|svg|mp4|webm|otf|ttf|woff|woff2)$/);

// Responsive Value Schema - Supports responsive breakpoint values
const ResponsiveValueSchema = z.object({
  mobile: z.string().optional(),
  tablet: z.string().optional(),
  desktop: z.string().optional(),
  '2xl': z.string().optional(),
}).or(z.string());

// ============================================================================
// ANIMATION SCHEMAS
// ============================================================================

// GSAP Easing Schema
const EasingSchema = z.enum([
  'none', 'power1.in', 'power1.out', 'power1.inOut',
  'power2.in', 'power2.out', 'power2.inOut',
  'power3.in', 'power3.out', 'power3.inOut',
  'power4.in', 'power4.out', 'power4.inOut',
  'back.in', 'back.out', 'back.inOut',
  'bounce.in', 'bounce.out', 'bounce.inOut',
  'circ.in', 'circ.out', 'circ.inOut',
  'elastic.in', 'elastic.out', 'elastic.inOut',
  'expo.in', 'expo.out', 'expo.inOut',
  'sine.in', 'sine.out', 'sine.inOut'
]);

// ScrollTrigger Configuration Schema
const ScrollTriggerSchema = z.object({
  trigger: z.string(),
  start: z.string(),
  end: z.string(),
  scrub: z.union([z.boolean(), z.number()]).optional(),
  pin: z.boolean().optional(),
  markers: z.boolean().optional(),
  toggleActions: z.string().optional(),
});

// Animation Timeline Schema
const AnimationTimelineSchema = z.object({
  delay: z.number().optional(),
  duration: z.number().optional(),
  ease: EasingSchema.optional(),
  stagger: z.number().optional(),
  scrollTrigger: ScrollTriggerSchema.optional(),
});

// Text Animation Schema (for SplitText)
const TextAnimationSchema = z.object({
  type: z.enum(['chars', 'words', 'lines']),
  from: z.object({
    yPercent: z.number().optional(),
    xPercent: z.number().optional(),
    opacity: z.number().optional(),
    scale: z.number().optional(),
    rotation: z.number().optional(),
  }).optional(),
  to: z.object({
    yPercent: z.number().optional(),
    xPercent: z.number().optional(),
    opacity: z.number().optional(),
    scale: z.number().optional(),
    rotation: z.number().optional(),
  }).optional(),
  timeline: AnimationTimelineSchema,
});

// ============================================================================
// CONTENT SCHEMAS
// ============================================================================

// Typography Schema
const TypographySchema = z.object({
  fontFamily: z.string(),
  fontSize: ResponsiveValueSchema,
  fontWeight: z.union([z.number(), z.string()]),
  lineHeight: z.string().optional(),
  letterSpacing: z.string().optional(),
  textTransform: z.enum(['none', 'uppercase', 'lowercase', 'capitalize']).optional(),
  color: ColorSchema,
});

// Button Schema
const ButtonSchema = z.object({
  text: z.string(),
  backgroundColor: ColorSchema,
  textColor: ColorSchema,
  padding: ResponsiveValueSchema,
  borderRadius: z.string(),
  fontSize: ResponsiveValueSchema,
  fontWeight: z.union([z.number(), z.string()]),
  href: z.string().optional(),
  onClick: z.string().optional(), // Function name or action
});

// ============================================================================
// SECTION SCHEMAS
// ============================================================================

// Hero Section Schema
const HeroSectionSchema = z.object({
  backgroundVideo: AssetPathSchema.optional(),
  backgroundImage: AssetPathSchema.optional(),
  title: z.string(),
  subtitle: z.string(),
  description: z.string(),
  button: ButtonSchema,
  titleAnimation: TextAnimationSchema,
  subtitleAnimation: TextAnimationSchema,
  scrollAnimation: z.object({
    rotate: z.number(),
    scale: z.number(),
    yPercent: z.number(),
    ease: EasingSchema,
  }),
});

// Flavor Schema
const FlavorSchema = z.object({
  name: z.string(),
  color: z.string(), // Color identifier for assets
  rotation: ResponsiveValueSchema,
  backgroundImage: AssetPathSchema,
  drinkImage: AssetPathSchema,
  elementsImage: AssetPathSchema,
});

// Flavor Section Schema
const FlavorSectionSchema = z.object({
  title: z.object({
    firstLine: z.string(),
    highlightText: z.string(),
    lastLine: z.string(),
  }),
  flavors: z.array(FlavorSchema),
  scrollAnimation: z.object({
    pinning: z.boolean(),
    horizontalScroll: z.boolean(),
    titleMovement: z.object({
      firstText: z.number(),
      highlightText: z.number(),
      lastText: z.number(),
    }),
  }),
});

// Nutrient Schema
const NutrientSchema = z.object({
  label: z.string(),
  amount: z.string(),
});

// Nutrition Section Schema
const NutritionSectionSchema = z.object({
  title: z.string(),
  description: z.string(),
  backgroundImage: AssetPathSchema,
  nutrients: z.array(NutrientSchema),
  responsiveNutrients: z.object({
    mobile: z.number(), // Number of nutrients to show on mobile
    desktop: z.number(),
  }),
});

// Benefit Schema
const BenefitSchema = z.object({
  title: z.string(),
  backgroundColor: ColorSchema,
  textColor: ColorSchema,
  borderColor: ColorSchema,
  rotation: z.string(),
  className: z.string(),
});

// Benefit Section Schema
const BenefitSectionSchema = z.object({
  description: z.string(),
  benefits: z.array(BenefitSchema),
  videoSection: z.object({
    videoPath: AssetPathSchema,
    circleTextImage: AssetPathSchema,
    playIconImage: AssetPathSchema,
  }),
});

// Testimonial Card Schema
const TestimonialCardSchema = z.object({
  name: z.string(),
  videoSrc: AssetPathSchema,
  profileImage: AssetPathSchema,
  rotation: z.string(),
  translation: z.string().optional(),
});

// Testimonial Section Schema
const TestimonialSectionSchema = z.object({
  title: z.object({
    firstWord: z.string(),
    secondWord: z.string(),
    thirdWord: z.string(),
  }),
  cards: z.array(TestimonialCardSchema),
  titleAnimation: z.object({
    firstWordMovement: z.number(),
    secondWordMovement: z.number(),
    thirdWordMovement: z.number(),
  }),
});

// Message Section Schema
const MessageSectionSchema = z.object({
  firstMessage: z.string(),
  secondMessage: z.string(),
  description: z.string(),
  backgroundColor: ColorSchema,
  textColor: ColorSchema,
  highlightText: z.string(),
  highlightBackground: ColorSchema,
});

// Social Link Schema
const SocialLinkSchema = z.object({
  platform: z.string(),
  icon: AssetPathSchema,
  url: z.string(),
});

// Footer Section Schema
const FooterSectionSchema = z.object({
  hashtag: z.string(),
  backgroundVideo: AssetPathSchema.optional(),
  backgroundImage: AssetPathSchema.optional(),
  socialLinks: z.array(SocialLinkSchema),
  navigation: z.object({
    flavors: z.array(z.string()),
    club: z.array(z.string()),
    company: z.array(z.string()),
  }),
  newsletter: z.object({
    description: z.string(),
    placeholder: z.string(),
    submitIcon: AssetPathSchema,
  }),
  copyright: z.object({
    text: z.string(),
    links: z.array(z.object({
      text: z.string(),
      url: z.string(),
    })),
  }),
});

// ============================================================================
// MAIN WEBSITE CONFIGURATION SCHEMA
// ============================================================================

export const WebsiteConfigSchema = z.object({
  meta: z.object({
    title: z.string(),
    description: z.string(),
    favicon: AssetPathSchema,
  }),
  
  branding: z.object({
    logo: AssetPathSchema,
    navLogo: AssetPathSchema,
    colors: z.object({
      primary: ColorSchema,
      secondary: ColorSchema,
      accent: ColorSchema,
      background: ColorSchema,
      text: ColorSchema,
    }),
    fonts: z.object({
      primary: z.string(),
      secondary: z.string(),
    }),
  }),

  layout: z.object({
    scrollSmoother: z.object({
      smooth: z.number(),
      effects: z.boolean(),
    }),
    breakpoints: z.object({
      mobile: z.string(),
      tablet: z.string(),
      desktop: z.string(),
      xl: z.string(),
      '2xl': z.string(),
    }),
  }),

  sections: z.object({
    hero: HeroSectionSchema,
    message: MessageSectionSchema,
    flavors: FlavorSectionSchema,
    nutrition: NutritionSectionSchema,
    benefits: BenefitSectionSchema,
    testimonials: TestimonialSectionSchema,
    footer: FooterSectionSchema,
  }),

  animations: z.object({
    globalSettings: z.object({
      defaultEase: EasingSchema,
      defaultDuration: z.number(),
      defaultStagger: z.number(),
    }),
    textReveal: z.object({
      charStagger: z.number(),
      wordStagger: z.number(),
      defaultYPercent: z.number(),
    }),
  }),
});

// Export individual schemas for modular use
export {
  ColorSchema,
  AssetPathSchema,
  ResponsiveValueSchema,
  EasingSchema,
  ScrollTriggerSchema,
  AnimationTimelineSchema,
  TextAnimationSchema,
  TypographySchema,
  ButtonSchema,
  HeroSectionSchema,
  FlavorSchema,
  FlavorSectionSchema,
  NutrientSchema,
  NutritionSectionSchema,
  BenefitSchema,
  BenefitSectionSchema,
  TestimonialCardSchema,
  TestimonialSectionSchema,
  MessageSectionSchema,
  SocialLinkSchema,
  FooterSectionSchema,
};
