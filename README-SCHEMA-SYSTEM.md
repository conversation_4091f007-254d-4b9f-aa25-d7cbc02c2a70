# GSAP Awwwards Website - Schema-Driven Configuration System

## Overview

This document provides a comprehensive schema-driven configuration system for the GSAP Awwwards Website, enabling programmatic content management, dynamic updates, and type-safe configuration handling.

## 🎯 Key Benefits

- **Type Safety**: Zod schemas ensure data integrity and catch errors early
- **Programmatic Control**: Modify website content via API calls
- **Validation**: Automatic validation of all configuration changes
- **Responsive Design**: Built-in responsive value handling
- **Asset Management**: Centralized asset path management and validation
- **Animation Control**: Configurable GSAP animations and timelines
- **Theme Management**: Dynamic color and typography systems
- **Developer Experience**: IntelliSense support and error catching

## 📁 File Structure

```
schemas/
├── website-config.js          # Main Zod schemas
config/
├── website-config.json        # Main configuration file
utils/
├── config-manager.js          # Configuration management utilities
├── migration-helper.js        # Migration utilities
hooks/
├── useWebsiteConfig.js        # React hooks for configuration
docs/
├── implementation-plan.md     # Detailed implementation guide
```

## 🚀 Quick Start

### 1. Install Dependencies

```bash
npm install zod
```

### 2. Setup Configuration Provider

```jsx
// src/main.jsx
import { ConfigProvider } from './hooks/useWebsiteConfig';

ReactDOM.createRoot(document.getElementById('root')).render(
  <ConfigProvider configSource="/config/website-config.json">
    <App />
  </ConfigProvider>
);
```

### 3. Use Configuration in Components

```jsx
// Example: HeroSection with configuration
import { useConfigSection, useThemeColors } from './hooks/useWebsiteConfig';

const HeroSection = () => {
  const { data: heroConfig } = useConfigSection('hero');
  const colors = useThemeColors();
  
  if (!heroConfig) return <div>Loading...</div>;
  
  return (
    <section style={{ backgroundColor: colors.background }}>
      <h1>{heroConfig.title}</h1>
      <p>{heroConfig.description}</p>
      <button 
        style={{ 
          backgroundColor: heroConfig.button.backgroundColor,
          color: heroConfig.button.textColor 
        }}
      >
        {heroConfig.button.text}
      </button>
    </section>
  );
};
```

## 📋 Schema Structure

### Core Schemas

- **WebsiteConfigSchema**: Main configuration schema
- **ColorSchema**: Color validation (hex, rgb, hsl, CSS variables)
- **AssetPathSchema**: Asset path validation
- **ResponsiveValueSchema**: Responsive breakpoint values
- **AnimationTimelineSchema**: GSAP animation configurations
- **ScrollTriggerSchema**: ScrollTrigger configurations

### Section Schemas

- **HeroSectionSchema**: Hero section configuration
- **FlavorSectionSchema**: Flavor showcase configuration
- **NutritionSectionSchema**: Nutrition facts configuration
- **BenefitSectionSchema**: Benefits section configuration
- **TestimonialSectionSchema**: Testimonials configuration
- **FooterSectionSchema**: Footer configuration
- **MessageSectionSchema**: Message section configuration

## 🔧 Configuration Management

### Loading Configuration

```javascript
import configManager from './utils/config-manager.js';

// Load from file
await configManager.loadConfig('/config/website-config.json');

// Get specific section
const heroConfig = configManager.getSection('hero');

// Update section
configManager.updateSection('hero', {
  ...heroConfig,
  title: 'New Amazing Title'
});
```

### React Hooks

```javascript
// Get full configuration
const { config, loading, error } = useWebsiteConfig();

// Get specific section
const { data: heroConfig, update } = useConfigSection('hero');

// Get responsive values
const buttonPadding = useResponsiveValue(heroConfig?.button?.padding);

// Get theme colors
const colors = useThemeColors();

// Get typography settings
const typography = useTypography();
```

## 🎨 Styling Integration

### CSS Custom Properties

```javascript
// Generate CSS variables from configuration
const cssVars = configManager.generateCSSCustomProperties();

// Use in components
const colors = useThemeColors();
const style = {
  backgroundColor: colors.primaryVar, // var(--color-primary, #523122)
  color: colors.textVar
};
```

### Responsive Values

```javascript
// Configuration supports responsive breakpoints
const buttonConfig = {
  padding: {
    mobile: "0.75rem 2.5rem",
    tablet: "1rem 3rem", 
    desktop: "1.25rem 4rem"
  }
};

// Hook automatically resolves based on current breakpoint
const padding = useResponsiveValue(buttonConfig.padding);
```

## 🎬 Animation Configuration

### GSAP Integration

```javascript
// Animation configuration in schema
const heroAnimation = {
  titleAnimation: {
    type: "chars",
    from: { yPercent: 200 },
    timeline: {
      delay: 1,
      stagger: 0.02,
      ease: "power2.out"
    }
  },
  scrollAnimation: {
    rotate: 7,
    scale: 0.9,
    yPercent: 30,
    ease: "power1.inOut"
  }
};

// Use in components
const animationConfig = useGSAPSettings('textReveal');
```

## 📦 Asset Management

### Asset Validation

```javascript
// Get all asset paths from configuration
const assetPaths = configManager.getAssetPaths();

// Validate all assets exist
const validation = await configManager.validateAssets();
console.log('Missing assets:', validation.missing);
```

### Asset Schema

```javascript
// Assets are validated with regex patterns
const AssetPathSchema = z.string().regex(
  /^\/(images|videos|fonts)\/.*\.(png|jpg|jpeg|webp|svg|mp4|webm|otf|ttf|woff|woff2)$/
);
```

## 🔄 Migration from Existing Code

### Automatic Migration

```javascript
import { migrateConstants, generateMigrationReport } from './utils/migration-helper.js';

// Migrate existing constants
const migratedConfig = migrateConstants();

// Generate migration report
const report = generateMigrationReport();

// Export migrated configuration
const configJson = await exportMigratedConfig('./config/website-config.json');
```

### Manual Migration Steps

1. **Identify hardcoded values** in components
2. **Map to schema structure** using provided schemas
3. **Update components** to use configuration hooks
4. **Test with fallbacks** during transition period
5. **Validate configuration** using Zod schemas

## 🌐 API Integration

### Configuration API

```javascript
// GET configuration
const config = await fetch('/api/config').then(r => r.json());

// UPDATE section
await fetch('/api/config/sections/hero', {
  method: 'PUT',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(newHeroConfig)
});

// VALIDATE configuration
const validation = await fetch('/api/config/validate', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(configToValidate)
});
```

## 🧪 Testing

### Schema Validation Tests

```javascript
import { WebsiteConfigSchema } from '../schemas/website-config.js';

test('validates complete configuration', () => {
  const validConfig = {/* valid config */};
  expect(() => WebsiteConfigSchema.parse(validConfig)).not.toThrow();
});
```

### Component Tests

```javascript
test('component renders with configuration', () => {
  const mockConfig = {/* mock config */};
  render(
    <ConfigProvider configSource={mockConfig}>
      <HeroSection />
    </ConfigProvider>
  );
  expect(screen.getByText(mockConfig.sections.hero.title)).toBeInTheDocument();
});
```

## 📈 Performance Optimization

- **Lazy Loading**: Load configuration sections on demand
- **Caching**: Configuration caching with TTL
- **Validation**: Client-side validation for immediate feedback
- **Bundling**: Separate configuration from main bundle

## 🔒 Type Safety

```typescript
// TypeScript support (optional)
import { z } from 'zod';
import { WebsiteConfigSchema } from './schemas/website-config.js';

type WebsiteConfig = z.infer<typeof WebsiteConfigSchema>;
type HeroSection = z.infer<typeof HeroSectionSchema>;
```

## 📚 Documentation

- **[Implementation Plan](docs/implementation-plan.md)**: Detailed implementation guide
- **[Schema Reference](schemas/website-config.js)**: Complete schema definitions
- **[Migration Guide](utils/migration-helper.js)**: Migration utilities and examples

## 🎉 Example Configuration

See `config/website-config.json` for a complete example configuration that demonstrates all schema features and can be used as a starting point for your implementation.

---

This schema system transforms your GSAP Awwwards Website into a fully configurable, type-safe, and maintainable application that can be updated programmatically without code changes.
