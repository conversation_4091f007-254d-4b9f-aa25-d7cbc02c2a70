import { WebsiteConfigSchema } from '../schemas/website-config.js';

/**
 * Configuration Manager for GSAP Awwwards Website
 * Provides utilities for loading, validating, and managing website configuration
 */

class ConfigManager {
  constructor() {
    this.config = null;
    this.isLoaded = false;
  }

  /**
   * Load and validate configuration from JSON file or object
   * @param {string|object} source - Path to JSON file or configuration object
   * @returns {Promise<object>} Validated configuration
   */
  async loadConfig(source) {
    try {
      let configData;
      
      if (typeof source === 'string') {
        // Load from file path
        const response = await fetch(source);
        if (!response.ok) {
          throw new Error(`Failed to load config from ${source}: ${response.statusText}`);
        }
        configData = await response.json();
      } else {
        // Use provided object
        configData = source;
      }

      // Validate against schema
      const validatedConfig = WebsiteConfigSchema.parse(configData);
      
      this.config = validatedConfig;
      this.isLoaded = true;
      
      console.log('✅ Configuration loaded and validated successfully');
      return validatedConfig;
      
    } catch (error) {
      console.error('❌ Configuration validation failed:', error);
      throw new Error(`Configuration validation failed: ${error.message}`);
    }
  }

  /**
   * Get the current configuration
   * @returns {object|null} Current configuration or null if not loaded
   */
  getConfig() {
    if (!this.isLoaded) {
      console.warn('⚠️ Configuration not loaded. Call loadConfig() first.');
      return null;
    }
    return this.config;
  }

  /**
   * Get a specific section of the configuration
   * @param {string} sectionName - Name of the section (e.g., 'hero', 'flavors')
   * @returns {object|null} Section configuration or null if not found
   */
  getSection(sectionName) {
    if (!this.isLoaded) {
      console.warn('⚠️ Configuration not loaded. Call loadConfig() first.');
      return null;
    }
    
    return this.config.sections?.[sectionName] || null;
  }

  /**
   * Get branding configuration
   * @returns {object|null} Branding configuration
   */
  getBranding() {
    return this.config?.branding || null;
  }

  /**
   * Get animation settings
   * @returns {object|null} Animation configuration
   */
  getAnimations() {
    return this.config?.animations || null;
  }

  /**
   * Get layout configuration
   * @returns {object|null} Layout configuration
   */
  getLayout() {
    return this.config?.layout || null;
  }

  /**
   * Update a specific section of the configuration
   * @param {string} sectionName - Name of the section to update
   * @param {object} newData - New data for the section
   * @returns {boolean} Success status
   */
  updateSection(sectionName, newData) {
    if (!this.isLoaded) {
      console.warn('⚠️ Configuration not loaded. Call loadConfig() first.');
      return false;
    }

    try {
      // Create a copy of the current config
      const updatedConfig = { ...this.config };
      updatedConfig.sections = { ...updatedConfig.sections };
      updatedConfig.sections[sectionName] = newData;

      // Validate the updated configuration
      const validatedConfig = WebsiteConfigSchema.parse(updatedConfig);
      
      this.config = validatedConfig;
      console.log(`✅ Section '${sectionName}' updated successfully`);
      return true;
      
    } catch (error) {
      console.error(`❌ Failed to update section '${sectionName}':`, error);
      return false;
    }
  }

  /**
   * Get responsive value based on current breakpoint
   * @param {string|object} value - Responsive value or string
   * @param {string} breakpoint - Current breakpoint ('mobile', 'tablet', 'desktop', etc.)
   * @returns {string} Resolved value for the breakpoint
   */
  getResponsiveValue(value, breakpoint = 'desktop') {
    if (typeof value === 'string') {
      return value;
    }
    
    if (typeof value === 'object' && value !== null) {
      // Try to get value for specific breakpoint, fallback to desktop, then mobile
      return value[breakpoint] || value.desktop || value.mobile || '';
    }
    
    return '';
  }

  /**
   * Generate CSS custom properties from configuration
   * @returns {string} CSS custom properties string
   */
  generateCSSCustomProperties() {
    if (!this.isLoaded) {
      console.warn('⚠️ Configuration not loaded. Call loadConfig() first.');
      return '';
    }

    const { colors, fonts } = this.config.branding;
    
    let css = ':root {\n';
    
    // Add color variables
    Object.entries(colors).forEach(([key, value]) => {
      css += `  --color-${key}: ${value};\n`;
    });
    
    // Add font variables
    Object.entries(fonts).forEach(([key, value]) => {
      css += `  --font-${key}: ${value};\n`;
    });
    
    css += '}\n';
    
    return css;
  }

  /**
   * Export current configuration as JSON
   * @param {boolean} pretty - Whether to format JSON with indentation
   * @returns {string} JSON string of current configuration
   */
  exportConfig(pretty = true) {
    if (!this.isLoaded) {
      console.warn('⚠️ Configuration not loaded. Call loadConfig() first.');
      return '{}';
    }
    
    return JSON.stringify(this.config, null, pretty ? 2 : 0);
  }

  /**
   * Validate a partial configuration object
   * @param {object} partialConfig - Partial configuration to validate
   * @param {string} schemaName - Name of the schema to validate against
   * @returns {boolean} Validation result
   */
  validatePartial(partialConfig, schemaName) {
    try {
      // Import specific schema based on name
      const schemas = {
        hero: HeroSectionSchema,
        flavors: FlavorSectionSchema,
        nutrition: NutritionSectionSchema,
        benefits: BenefitSectionSchema,
        testimonials: TestimonialSectionSchema,
        footer: FooterSectionSchema,
        message: MessageSectionSchema,
      };
      
      const schema = schemas[schemaName];
      if (!schema) {
        console.error(`❌ Unknown schema: ${schemaName}`);
        return false;
      }
      
      schema.parse(partialConfig);
      console.log(`✅ Partial configuration for '${schemaName}' is valid`);
      return true;
      
    } catch (error) {
      console.error(`❌ Validation failed for '${schemaName}':`, error);
      return false;
    }
  }

  /**
   * Get asset paths used in the configuration
   * @returns {string[]} Array of asset paths
   */
  getAssetPaths() {
    if (!this.isLoaded) {
      console.warn('⚠️ Configuration not loaded. Call loadConfig() first.');
      return [];
    }

    const assets = new Set();
    
    // Recursively find asset paths in the configuration
    const findAssets = (obj) => {
      if (typeof obj === 'string' && obj.match(/^\/(images|videos|fonts)\//)) {
        assets.add(obj);
      } else if (typeof obj === 'object' && obj !== null) {
        Object.values(obj).forEach(findAssets);
      }
    };
    
    findAssets(this.config);
    
    return Array.from(assets).sort();
  }

  /**
   * Check if all referenced assets exist
   * @returns {Promise<object>} Object with missing and existing assets
   */
  async validateAssets() {
    const assetPaths = this.getAssetPaths();
    const results = {
      existing: [],
      missing: [],
      total: assetPaths.length
    };
    
    for (const assetPath of assetPaths) {
      try {
        const response = await fetch(assetPath, { method: 'HEAD' });
        if (response.ok) {
          results.existing.push(assetPath);
        } else {
          results.missing.push(assetPath);
        }
      } catch (error) {
        results.missing.push(assetPath);
      }
    }
    
    if (results.missing.length > 0) {
      console.warn(`⚠️ Missing assets found:`, results.missing);
    } else {
      console.log('✅ All assets are available');
    }
    
    return results;
  }
}

// Create singleton instance
const configManager = new ConfigManager();

export default configManager;

// Export utility functions
export {
  ConfigManager,
};

// Example usage:
/*
import configManager from './utils/config-manager.js';

// Load configuration
await configManager.loadConfig('/config/website-config.json');

// Get specific sections
const heroConfig = configManager.getSection('hero');
const flavorsConfig = configManager.getSection('flavors');

// Update a section
configManager.updateSection('hero', {
  ...heroConfig,
  title: 'New Amazing Title'
});

// Generate CSS custom properties
const cssVars = configManager.generateCSSCustomProperties();

// Validate assets
const assetValidation = await configManager.validateAssets();
*/
