/**
 * Migration Helper Utilities
 * Helps convert existing hardcoded data to schema-compliant configuration
 */

import { flavorlists, nutrientLists, cards } from '../src/constants/index.js';
import { WebsiteConfigSchema } from '../schemas/website-config.js';

/**
 * Convert existing constants to new configuration format
 */
export const migrateConstants = () => {
  const migratedConfig = {
    meta: {
      title: "SPYLT - Freaking Delicious Protein + Caffeine",
      description: "Live life to the fullest with SPYLT: Shatter boredom and embrace your inner kid with every deliciously smooth chug.",
      favicon: "/vite.svg"
    },
    
    branding: {
      logo: "/images/logo.png",
      navLogo: "/images/nav-logo.svg",
      colors: {
        primary: "#523122",
        secondary: "#a26833", 
        accent: "#e3a458",
        background: "#faeade",
        text: "#222123"
      },
      fonts: {
        primary: "Antonio, sans-serif",
        secondary: "ProximaNova, sans-serif"
      }
    },

    layout: {
      scrollSmoother: {
        smooth: 3,
        effects: true
      },
      breakpoints: {
        mobile: "768px",
        tablet: "1024px", 
        desktop: "1280px",
        xl: "1536px",
        "2xl": "1920px"
      }
    },

    sections: {
      hero: {
        backgroundVideo: "/videos/hero-bg.mp4",
        backgroundImage: "/images/hero-bg.png",
        title: "Freaking Delicious",
        subtitle: "Protein + Caffine",
        description: "Live life to the fullest with SPYLT: Shatter boredom and embrace your inner kid with every deliciously smooth chug.",
        button: {
          text: "Chug a SPYLT",
          backgroundColor: "#e3a458",
          textColor: "#523122",
          padding: {
            mobile: "0.75rem 2.5rem",
            desktop: "1.25rem 4rem"
          },
          borderRadius: "9999px",
          fontSize: {
            mobile: "1rem",
            desktop: "1.125rem"
          },
          fontWeight: "bold"
        },
        titleAnimation: {
          type: "chars",
          from: { yPercent: 200 },
          timeline: {
            delay: 1,
            stagger: 0.02,
            ease: "power2.out"
          }
        },
        subtitleAnimation: {
          type: "chars",
          timeline: {
            duration: 1,
            ease: "circ.out"
          }
        },
        scrollAnimation: {
          rotate: 7,
          scale: 0.9,
          yPercent: 30,
          ease: "power1.inOut"
        }
      },

      message: {
        firstMessage: "We're not just another protein drink",
        secondMessage: "We're a lifestyle revolution in a can",
        description: "SPYLT isn't just about nutrition—it's about breaking free from the ordinary and embracing the extraordinary in every moment.",
        backgroundColor: "#7f3b2d",
        textColor: "#faeade",
        highlightText: "freaking",
        highlightBackground: "#a26833"
      },

      flavors: {
        title: {
          firstLine: "We have 6",
          highlightText: "freaking",
          lastLine: "delicious flavors"
        },
        flavors: flavorlists.map(flavor => ({
          name: flavor.name,
          color: flavor.color,
          rotation: typeof flavor.rotation === 'string' ? 
            { mobile: "rotate-0", desktop: flavor.rotation } : 
            flavor.rotation,
          backgroundImage: `/images/${flavor.color}-bg.svg`,
          drinkImage: `/images/${flavor.color}-drink.webp`,
          elementsImage: `/images/${flavor.color}-elements.webp`
        })),
        scrollAnimation: {
          pinning: true,
          horizontalScroll: true,
          titleMovement: {
            firstText: -30,
            highlightText: -22,
            lastText: -10
          }
        }
      },

      nutrition: {
        title: "Packed with Essential Nutrients",
        description: "Every can of SPYLT is carefully crafted to deliver the nutrition your body craves.",
        backgroundImage: "/images/big-img.png",
        nutrients: nutrientLists,
        responsiveNutrients: {
          mobile: 3,
          desktop: 5
        }
      },

      benefits: {
        description: "Unlock the Advantages: Explore the Key Benefits of Choosing SPYLT",
        benefits: [
          {
            title: "Shelf stable",
            backgroundColor: "#c88e64",
            textColor: "#faeade",
            borderColor: "#222123",
            rotation: "rotate-[3deg]",
            className: "first-title"
          },
          {
            title: "Protein + Caffeine",
            backgroundColor: "#faeade",
            textColor: "#222123",
            borderColor: "#222123",
            rotation: "rotate-[-1deg]",
            className: "second-title"
          },
          {
            title: "Infinitely recyclable",
            backgroundColor: "#7F3B2D",
            textColor: "#faeade",
            borderColor: "#222123",
            rotation: "rotate-[1deg]",
            className: "third-title"
          },
          {
            title: "Lactose free",
            backgroundColor: "#FED775",
            textColor: "#2E2D2F",
            borderColor: "#222123",
            rotation: "rotate-[-5deg]",
            className: "fourth-title"
          }
        ],
        videoSection: {
          videoPath: "/videos/pin-video.mp4",
          circleTextImage: "/images/circle-text.svg",
          playIconImage: "/images/play.svg"
        }
      },

      testimonials: {
        title: {
          firstWord: "What's",
          secondWord: "Everyone",
          thirdWord: "Talking"
        },
        cards: cards.map(card => ({
          name: card.name,
          videoSrc: card.src,
          profileImage: card.img,
          rotation: card.rotation,
          translation: card.translation
        })),
        titleAnimation: {
          firstWordMovement: 70,
          secondWordMovement: 25,
          thirdWordMovement: -50
        }
      },

      footer: {
        hashtag: "#CHUGRESPONSIBLY",
        backgroundVideo: "/videos/splash.mp4",
        backgroundImage: "/images/footer-drink.png",
        socialLinks: [
          {
            platform: "YouTube",
            icon: "/images/yt.svg",
            url: "https://youtube.com"
          },
          {
            platform: "Instagram", 
            icon: "/images/insta.svg",
            url: "https://instagram.com"
          },
          {
            platform: "TikTok",
            icon: "/images/tiktok.svg",
            url: "https://tiktok.com"
          }
        ],
        navigation: {
          flavors: ["SPYLT Flavors"],
          club: ["Chug Club", "Student Marketing", "Dairy Dealers"],
          company: ["Company", "Contacts", "Tasty Talk"]
        },
        newsletter: {
          description: "Get Exclusive Early Access and Stay Informed About Product Updates, Events, and More!",
          placeholder: "Enter your email",
          submitIcon: "/images/arrow.svg"
        },
        copyright: {
          text: "Copyright © 2025 Spylt - All Rights Reserved",
          links: [
            { text: "Privacy Policy", url: "/privacy" },
            { text: "Terms of Service", url: "/terms" }
          ]
        }
      }
    },

    animations: {
      globalSettings: {
        defaultEase: "power1.inOut",
        defaultDuration: 1,
        defaultStagger: 0.02
      },
      textReveal: {
        charStagger: 0.02,
        wordStagger: 0.01,
        defaultYPercent: 200
      }
    }
  };

  return migratedConfig;
};

/**
 * Validate migrated configuration
 */
export const validateMigratedConfig = (config) => {
  try {
    const validatedConfig = WebsiteConfigSchema.parse(config);
    console.log('✅ Migration validation successful');
    return { success: true, config: validatedConfig, errors: [] };
  } catch (error) {
    console.error('❌ Migration validation failed:', error);
    return { success: false, config: null, errors: error.errors };
  }
};

/**
 * Generate migration report
 */
export const generateMigrationReport = () => {
  const migratedConfig = migrateConstants();
  const validation = validateMigratedConfig(migratedConfig);
  
  const report = {
    timestamp: new Date().toISOString(),
    originalData: {
      flavors: flavorlists.length,
      nutrients: nutrientLists.length,
      testimonials: cards.length
    },
    migratedData: {
      sections: Object.keys(migratedConfig.sections).length,
      flavors: migratedConfig.sections.flavors.flavors.length,
      nutrients: migratedConfig.sections.nutrition.nutrients.length,
      testimonials: migratedConfig.sections.testimonials.cards.length
    },
    validation: validation.success,
    errors: validation.errors
  };
  
  console.log('📊 Migration Report:', report);
  return report;
};

/**
 * Export migrated configuration to file
 */
export const exportMigratedConfig = async (outputPath = './config/migrated-config.json') => {
  const migratedConfig = migrateConstants();
  const validation = validateMigratedConfig(migratedConfig);
  
  if (!validation.success) {
    throw new Error('Cannot export invalid configuration');
  }
  
  const configJson = JSON.stringify(migratedConfig, null, 2);
  
  // In a Node.js environment, you would write to file:
  // await fs.writeFile(outputPath, configJson, 'utf8');
  
  // For browser environment, return the JSON string
  return configJson;
};

// Example usage:
/*
import { migrateConstants, validateMigratedConfig, generateMigrationReport } from './utils/migration-helper.js';

// Generate migrated configuration
const migratedConfig = migrateConstants();

// Validate the migration
const validation = validateMigratedConfig(migratedConfig);

// Generate a report
const report = generateMigrationReport();

// Export to file (in Node.js environment)
const configJson = await exportMigratedConfig('./config/website-config.json');
*/
